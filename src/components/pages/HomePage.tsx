'use client';

import Link from 'next/link';
import { TemplateManager } from '@/lib/template-manager';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PageLayout from '@/components/layout/PageLayout';

import { Zap, Download, Sparkles, Award, FileText, Users, Star, CheckCircle, Clock, Shield } from 'lucide-react';

export default function HomePage() {
  // 获取所有分类数据（包括禁用的，用于显示Coming Soon）
  let categories: any[] = [];
  try {
    categories = TemplateManager.getAllCategoriesForDisplay();
  } catch (error) {
    console.error('Error loading categories:', error);
    categories = [];
  }

  return (
    <PageLayout>
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 sm:py-16 lg:py-20">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <div className="flex justify-center mb-6">
                <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
                  <Sparkles className="w-4 h-4 mr-2" />
                  #1 Free Certificate Maker
                </Badge>
              </div>

              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Free Online
                <span className="text-blue-600 block sm:inline sm:ml-3">
                  Certificate Maker
                </span>
                <span className="block text-2xl sm:text-3xl lg:text-4xl text-gray-700 mt-2 font-semibold">
                  Create Professional Certificates Instantly
                </span>
              </h1>

              <p className="text-lg sm:text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                Design and generate professional certificates online for free with our certificate maker.
                Choose from premium certificate templates, customize with your details, and download high-quality PDF certificates instantly.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-10">
                <Link href="/certificate-templates/">
                  <Button size="lg" className="w-full sm:w-auto text-lg px-8 py-4 h-auto min-h-[52px] bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200">
                    <Award className="w-5 h-5 mr-2" />
                    Create Certificate Now
                  </Button>
                </Link>

                <Link href="#templates">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto text-lg px-8 py-4 h-auto min-h-[52px] border-2 hover:bg-gray-50">
                    <FileText className="w-5 h-5 mr-2" />
                    Browse Templates
                  </Button>
                </Link>
              </div>

              {/* Trust indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
                <div className="flex flex-col items-center p-3 bg-white rounded-lg shadow-sm">
                  <CheckCircle className="w-6 h-6 mb-2 text-green-500" />
                  <span className="font-semibold text-gray-900 text-sm">100% Free</span>
                  <span className="text-xs text-gray-600">No hidden costs</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-white rounded-lg shadow-sm">
                  <Clock className="w-6 h-6 mb-2 text-blue-500" />
                  <span className="font-semibold text-gray-900 text-sm">Instant Download</span>
                  <span className="text-xs text-gray-600">Ready in minutes</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-white rounded-lg shadow-sm">
                  <Shield className="w-6 h-6 mb-2 text-purple-500" />
                  <span className="font-semibold text-gray-900 text-sm">No Registration</span>
                  <span className="text-xs text-gray-600">Start immediately</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-12 sm:py-16 bg-white" aria-labelledby="features-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="features-heading" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Why Choose Our Free Certificate Maker?
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Create professional certificates with ease using our powerful online certificate generator and template library
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="text-center p-6 hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Award className="w-6 h-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-lg font-bold">Professional Templates</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-sm">
                    Choose from our collection of expertly designed certificate templates for achievements,
                    course completions, event participation, and excellence awards.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-6 hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Zap className="w-6 h-6 text-green-600" />
                  </div>
                  <CardTitle className="text-lg font-bold">Instant Generation</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-sm">
                    Generate your certificates in seconds. Simply fill in the details, customize the design,
                    and download your high-quality PDF certificate instantly.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-6 hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Download className="w-6 h-6 text-purple-600" />
                  </div>
                  <CardTitle className="text-lg font-bold">High-Quality PDFs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-sm">
                    Download print-ready PDF certificates with crisp text and professional formatting.
                    Perfect for printing or digital sharing.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-6 hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Users className="w-6 h-6 text-yellow-600" />
                  </div>
                  <CardTitle className="text-lg font-bold">Multiple Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-sm">
                    Create certificates for various purposes including academic achievements,
                    course completions, event participation, and excellence recognition.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-6 hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Star className="w-6 h-6 text-red-600" />
                  </div>
                  <CardTitle className="text-lg font-bold">Easy Customization</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-sm">
                    Personalize your certificates with custom names, dates, signatures, and detailed descriptions.
                    No design skills required.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-6 hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Shield className="w-6 h-6 text-indigo-600" />
                  </div>
                  <CardTitle className="text-lg font-bold">Completely Free</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-sm">
                    Create unlimited certificates at no cost. No subscriptions, no hidden fees,
                    and no registration required to get started.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>



        {/* How It Works Section */}
        <section id="how-it-works" className="py-12 sm:py-16 bg-gray-50" aria-labelledby="how-it-works-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="how-it-works-heading" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                How to Create Your Certificate Online
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Generate professional certificates in just 3 simple steps with our free certificate maker
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-white">1</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Choose Template</h3>
                <p className="text-gray-600">
                  Browse our collection of professional certificate templates.
                  Select the perfect design for your achievement, completion, or participation certificate.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-white">2</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Customize Details</h3>
                <p className="text-gray-600">
                  Fill in the recipient name, achievement details, date, and signature.
                  Our smart form guides you through each field with helpful suggestions.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-white">3</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Download PDF</h3>
                <p className="text-gray-600">
                  Generate and download your high-quality PDF certificate instantly.
                  Print it or share it digitally - it&apos;s ready to use immediately.
                </p>
              </div>
            </div>

            <div className="text-center mt-10">
              <Link href="/certificate-templates/">
                <Button size="lg" className="text-lg px-8 py-4 h-auto min-h-[52px] bg-blue-600 hover:bg-blue-700">
                  <Award className="w-5 h-5 mr-2" />
                  Start Creating Now
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Templates Preview Section */}
        <section id="templates" className="py-12 sm:py-16 bg-white" aria-labelledby="templates-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="templates-heading" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Free Certificate Templates
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Choose from our professionally designed certificate templates for every occasion and achievement type
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.length > 0 && categories.slice(0, 4).map((category) => {
                const isDisabled = category.enabled === false;

                const cardContent = (
                  <Card className={`transition-shadow duration-200 relative overflow-hidden ${
                    isDisabled
                      ? 'opacity-75 cursor-not-allowed'
                      : 'hover:shadow-lg cursor-pointer'
                  }`}>
                    {/* Coming Soon 遮罩层 */}
                    {isDisabled && (
                      <div className="absolute inset-0 bg-black/60 z-10 flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="text-lg font-bold mb-1">Coming Soon</div>
                          <div className="text-sm opacity-90">即将推出</div>
                        </div>
                      </div>
                    )}

                    <CardHeader className="text-center pb-4 relative z-20">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Award className="w-6 h-6 text-white" />
                      </div>
                      <CardTitle className={`text-base ${isDisabled ? 'text-gray-600' : ''}`}>
                        {category.displayName}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0 relative z-20">
                      <CardDescription className={`text-center text-sm ${
                        isDisabled ? 'text-gray-500' : ''
                      }`}>
                        {category.description}
                      </CardDescription>
                      <div className="text-center mt-3">
                        <Badge variant="secondary" className="text-xs">
                          {category.templateCount} Templates
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                );

                // 根据是否禁用来决定是否包装在Link中
                if (isDisabled) {
                  return (
                    <div key={category.id}>
                      {cardContent}
                    </div>
                  );
                }

                return (
                  <Link key={category.id} href={`/certificate-templates/${category.urlSlug}/`}>
                    {cardContent}
                  </Link>
                );
              })}
              {categories.length === 0 && (
                <div className="col-span-full text-center py-8">
                  <p className="text-gray-600">Templates are being prepared. Please check back soon!</p>
                </div>
              )}
            </div>

            <div className="text-center mt-10">
              <Link href="/certificate-templates/">
                <Button variant="outline" size="lg" className="text-lg px-8 py-4 h-auto min-h-[52px] border-2">
                  <FileText className="w-5 h-5 mr-2" />
                  View All Templates
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-12 sm:py-16 bg-gray-50" aria-labelledby="faq-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="faq-heading" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Everything you need to know about our free online certificate maker
              </p>
            </header>

            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Is the certificate maker really free?</h3>
                  <p className="text-gray-600 text-sm">
                    Yes! Our certificate maker is completely free to use. You can create unlimited certificates
                    without any cost, subscription, or hidden fees.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">What formats can I download?</h3>
                  <p className="text-gray-600 text-sm">
                    All certificates are generated as high-quality PDF files that are perfect for printing
                    or digital sharing. PDFs maintain their quality at any size.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Do I need to register an account?</h3>
                  <p className="text-gray-600 text-sm">
                    No registration required! You can start creating certificates immediately without
                    providing any personal information or creating an account.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Can I customize the certificate design?</h3>
                  <p className="text-gray-600 text-sm">
                    You can customize all text fields including recipient name, achievement details,
                    date, and signature. The visual design is professionally optimized for best results.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Are the certificates print-ready?</h3>
                  <p className="text-gray-600 text-sm">
                    Absolutely! All certificates are generated in high-resolution PDF format that&apos;s
                    perfect for professional printing on standard paper sizes.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">How long does it take to create a certificate?</h3>
                  <p className="text-gray-600 text-sm">
                    Creating a certificate takes less than 2 minutes. Simply choose a template,
                    fill in your details, and download your professional certificate instantly.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-12 sm:py-16 bg-blue-600">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-4">
              Ready to Create Your Certificate?
            </h2>
            <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of users who trust our certificate maker for their professional needs.
              Start creating beautiful certificates today - it&apos;s completely free!
            </p>

            <div className="flex justify-center">
              <Link href="/certificate-templates/">
                <Button size="lg" className="text-lg px-8 py-4 h-auto min-h-[52px] bg-white text-blue-600 hover:bg-gray-100">
                  <Award className="w-5 h-5 mr-2" />
                  Create Your Certificate
                </Button>
              </Link>
            </div>
          </div>
        </section>


      </div>
    </PageLayout>
  );
}
