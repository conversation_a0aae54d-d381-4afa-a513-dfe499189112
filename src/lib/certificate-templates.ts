import { CertificateTemplate, CertificateCategory } from '@/types/certificate';

/**
 * 证书模板配置
 * 基于文档中的精确规格定义
 */

export const CERTIFICATE_TEMPLATES: CertificateTemplate[] = [
  // Achievement Category Template 1
  {
    id: 'achievement-template-1',
    name: 'achievement-template-1',
    displayName: 'Professional Achievement Certificate',
    description: 'Professional blue-themed certificate with elegant borders',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['professional', 'business', 'blue', 'elegant', 'formal'],

    // 视觉属性
    preview: '/templates/1.png',
    backgroundImage: '/templates/1.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Business Achievement Certificate Template | Professional Blue Design',
    seoDescription: 'Create professional achievement certificates with our classic business template. Features elegant blue design with customizable text fields. Download as high-quality PDF.',
    seoKeywords: ['classic business certificate template', 'professional blue certificate', 'business achievement award', 'corporate recognition certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #1E40AF',
      colors: {
        primary: '#1E40AF',
        secondary: '#3B82F6',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#1E40AF',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 28,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },
    layout: {
      name: {
        x: 297.64,
        y: 300,
        width: 400,
        height: 40,
        align: 'center',
        fontSize: 28,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 400,
        width: 450,
        height: 100,
        align: 'center',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 2
  {
    id: 'achievement-template-2',
    name: 'achievement-template-2',
    displayName: 'Distinguished Achievement Certificate',
    description: 'Distinguished certificate design for outstanding achievements',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['distinguished', 'achievement', 'premium', 'elegant', 'formal'],

    // 视觉属性
    preview: '/templates/4.jpg',
    backgroundImage: '/templates/4.jpg',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Distinguished Achievement Certificate Template | Premium Professional Design',
    seoDescription: 'Create distinguished achievement certificates with our premium template. Perfect for outstanding performance recognition and professional awards.',
    seoKeywords: ['distinguished achievement certificate', 'premium certificate design', 'professional achievement award', 'outstanding performance certificate'],
    style: {
      background: '#FFFFFF',
      border: '1pt solid #6B7280',
      colors: {
        primary: '#000000',
        secondary: '#6B7280',
        background: '#FFFFFF',
        text: '#374151',
        border: '#6B7280',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 26,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Inter',
          size: 16,
          weight: 500,
          color: '#6B7280',
        },
      },
    },
    layout: {
      name: {
        x: 297.64,
        y: 320,
        width: 400,
        height: 35,
        align: 'center',
        fontSize: 26,
        fontFamily: 'Inter',
        color: '#000000',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 420,
        width: 450,
        height: 80,
        align: 'center',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 600,
        width: 150,
        height: 25,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 600,
        width: 150,
        height: 25,
        align: 'right',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#6B7280',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 3 (Copy of Template 1 with different preview)
  {
    id: 'achievement-template-3',
    name: 'achievement-template-3',
    displayName: 'Golden Achievement Certificate',
    description: 'Professional golden-themed certificate with elegant borders',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['professional', 'business', 'golden', 'elegant', 'formal'],

    // 视觉属性
    preview: '/templates/3.jpg',
    backgroundImage: '/templates/3.jpg',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Golden Achievement Certificate Template | Professional Design',
    seoDescription: 'Create professional achievement certificates with our golden template. Features elegant design with customizable text fields. Download as high-quality PDF.',
    seoKeywords: ['golden achievement certificate template', 'professional certificate', 'business achievement award', 'corporate recognition certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #D97706',
      colors: {
        primary: '#D97706',
        secondary: '#F59E0B',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#D97706',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 32,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 400,
        y: 300,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 32,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 400,
        y: 350,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 4 (Copy of Template 2 with different preview)
  {
    id: 'achievement-template-4',
    name: 'achievement-template-4',
    displayName: 'Classic Achievement Certificate',
    description: 'Clean and classic certificate design with minimalist aesthetics',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['classic', 'minimalist', 'clean', 'contemporary', 'simple'],

    // 视觉属性
    preview: '/templates/4 - Copy.jpg',
    backgroundImage: '/templates/4 - Copy.jpg',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Achievement Certificate Template | Minimalist Design',
    seoDescription: 'Create clean achievement certificates with our classic template. Features minimalist design with professional typography. Perfect for modern businesses.',
    seoKeywords: ['classic achievement certificate template', 'minimalist certificate', 'clean certificate design', 'modern business certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '2pt solid #6B7280',
      colors: {
        primary: '#374151',
        secondary: '#6B7280',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#6B7280',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 28,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#6B7280',
        },
        signature: {
          family: 'Inter',
          size: 18,
          weight: 500,
          color: '#374151',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 400,
        y: 300,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 28,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 400,
        y: 350,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#6B7280',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#6B7280',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 5 (Copy of Template 1 with different preview)
  {
    id: 'achievement-template-5',
    name: 'achievement-template-5',
    displayName: 'Creative Achievement Certificate',
    description: 'Creative purple-themed certificate with modern design',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['creative', 'modern', 'purple', 'artistic', 'contemporary'],

    // 视觉属性
    preview: '/templates/1 - Copy.png',
    backgroundImage: '/templates/1 - Copy.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Creative Achievement Certificate Template | Modern Design',
    seoDescription: 'Create modern achievement certificates with our creative template. Features artistic design with vibrant colors. Perfect for creative industries.',
    seoKeywords: ['creative achievement certificate template', 'modern certificate', 'artistic certificate design', 'purple certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #7C3AED',
      colors: {
        primary: '#7C3AED',
        secondary: '#A855F7',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#7C3AED',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 32,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 400,
        y: 300,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 32,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 400,
        y: 350,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },
{
    id: 'achievement-template-6',
    name: 'achievement-template-6',
    displayName: 'Professional Achievement Certificate',
    description: 'Professional blue-themed certificate with elegant borders',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['professional', 'business', 'blue', 'elegant', 'formal'],

    // 视觉属性
    preview: '/templates/1.png',
    backgroundImage: '/templates/1.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Business Achievement Certificate Template | Professional Blue Design',
    seoDescription: 'Create professional achievement certificates with our classic business template. Features elegant blue design with customizable text fields. Download as high-quality PDF.',
    seoKeywords: ['classic business certificate template', 'professional blue certificate', 'business achievement award', 'corporate recognition certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #1E40AF',
      colors: {
        primary: '#1E40AF',
        secondary: '#3B82F6',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#1E40AF',
      },
      fonts: {
        
        name: {
          family: 'Playfair Display',
          size: 28,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },
    layout: {
      name: {
        x: 297.64,
        y: 300,
        width: 400,
        height: 40,
        align: 'center',
        fontSize: 28,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 400,
        width: 450,
        height: 100,
        align: 'center',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },
  // Completion Category Template 1
  {
    id: 'completion-template-1',
    name: 'completion-template-1',
    displayName: 'Professional Completion Certificate',
    description: 'Clean and professional design for course completion',

    // 分类信息
    category: CertificateCategory.COMPLETION,
    tags: ['professional', 'completion', 'clean', 'modern', 'educational'],

    // 视觉属性
    preview: '/templates/completion1.png',
    backgroundImage: '/templates/completion1-blank.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Professional Completion Certificate Template | Course & Training Certificates',
    seoDescription: 'Create professional completion certificates for courses and training programs. Clean design perfect for educational achievements.',
    seoKeywords: ['professional completion certificate', 'course completion template', 'training certificate', 'educational certificate'],
    style: {
      background: '#F0FDF4',
      border: '2pt solid #059669',
      colors: {
        primary: '#059669',
        secondary: '#10B981',
        background: '#F0FDF4',
        text: '#374151',
        border: '#059669',
      },
      fonts: {
        name: {
          family: 'Dancing Script',
          size: 26,
          weight: 400,
          color: '#1F2937',
        },
        body: {
          family: 'Dancing Script',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 22,
          weight: 400,
          color: '#059669',
        },
      },
    },
    layout: {
      name: {
        x: 297.5,
        y: 450,
        width: 400,
        height: 40,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
      details: {
        x: 297.5,
        y: 550,
        width: 450,
        height: 120,
        align: 'center',
        fontSize: 20,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 100,
        y: 700,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 495,
        y: 700,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Completion Category Template 2
  {
    id: 'completion-template-2',
    name: 'completion-template-2',
    displayName: 'Professional Completion Certificate',
    description: 'Clean and professional design for course completion',

    // 分类信息
    category: CertificateCategory.COMPLETION,
    tags: ['professional', 'completion', 'clean', 'modern', 'educational'],

    // 视觉属性
    preview: '/templates/completion2.png',
    backgroundImage: '/templates/completion2-blank.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Professional Completion Certificate Template | Course & Training Certificates',
    seoDescription: 'Create professional completion certificates for courses and training programs. Clean design perfect for educational achievements.',
    seoKeywords: ['professional completion certificate', 'course completion template', 'training certificate', 'educational certificate'],
    style: {
      background: '#F0FDF4',
      border: '2pt solid #059669',
      colors: {
        primary: '#059669',
        secondary: '#10B981',
        background: '#F0FDF4',
        text: '#374151',
        border: '#059669',
      },
      fonts: {
        
        name: {
          family: 'Dancing Script',
          size: 26,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Dancing Script',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 22,
          weight: 400,
          color: '#374151',
        },
      },
    },
    layout: {
      
      name: {
        x: 297.64,
        y: 460,
        width: 400,
        height: 35,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 560,
        width: 450,
        height: 90,
        align: 'center',
        fontSize: 20,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 130,
        y: 700,
        width: 150,
        height: 28,
        align: 'left',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 465,
        y: 700,
        width: 150,
        height: 28,
        align: 'right',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },
  
  
  // Completion Category Template 3
  {
    id: 'completion-template-3',
    name: 'completion-template-3',
    displayName: 'Professional Completion Certificate',
    description: 'Clean and professional design for course completion',

    // 分类信息
    category: CertificateCategory.COMPLETION,
    tags: ['professional', 'completion', 'clean', 'modern', 'educational'],

    // 视觉属性
    preview: '/templates/completion3.png',
    backgroundImage: '/templates/completion3-blank.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Professional Completion Certificate Template | Course & Training Certificates',
    seoDescription: 'Create professional completion certificates for courses and training programs. Clean design perfect for educational achievements.',
    seoKeywords: ['professional completion certificate', 'course completion template', 'training certificate', 'educational certificate'],
    style: {
      background: '#F0FDF4',
      border: '2pt solid #059669',
      colors: {
        primary: '#059669',
        secondary: '#10B981',
        background: '#F0FDF4',
        text: '#374151',
        border: '#059669',
      },
      fonts: {
        name: {
          family: 'Dancing Script',
          size: 38,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Dancing Script',
          size: 20,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 22,
          weight: 400,
          color: '#374151',
        },
      },
    },
    layout: {
      
      name: {
        x: 297.64,
        y: 530,
        width: 400,
        height: 35,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 630,
        width: 450,
        height: 90,
        align: 'center',
        fontSize: 20,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 130,
        y: 690,
        width: 150,
        height: 28,
        align: 'left',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 465,
        y: 690,
        width: 150,
        height: 28,
        align: 'right',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },


  // Participation Category Template 1
  {
    id: 'participation-template-1',
    name: 'participation-template-1',
    displayName: 'Event Participation Certificate',
    description: 'Professional certificate for event and workshop participation',

    // 分类信息
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'event', 'workshop', 'professional', 'modern'],

    // 视觉属性
    preview: '/templates/participation-1.png',
    backgroundImage: '/templates/participation-1.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Event Participation Certificate Template | Workshop & Conference Certificates',
    seoDescription: 'Create professional participation certificates for events, workshops, and conferences. Perfect for acknowledging attendance and engagement.',
    seoKeywords: ['event participation certificate', 'workshop certificate template', 'conference participation', 'professional event certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      
      name: {
        x: 300,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 100,
        y: 800,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 500,
        y: 800,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 300,
        y: 450,
        width: 400,
        height: 100,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Participation Category Template 2
  {
    id: 'participation-template-2',
    name: 'participation-template-2',
    displayName: 'Workshop Participation Certificate',
    description: 'Elegant certificate for workshop and seminar participation',

    // 分类信息
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'workshop', 'seminar', 'elegant', 'professional'],

    // 视觉属性
    preview: '/templates/participation-2.png',
    backgroundImage: '/templates/participation-2.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Workshop Participation Certificate Template | Seminar & Training Certificates',
    seoDescription: 'Create elegant participation certificates for workshops and seminars. Perfect for acknowledging learning engagement and professional development.',
    seoKeywords: ['workshop participation certificate', 'seminar certificate template', 'training participation', 'professional development certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      
      name: {
        x: 300,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 100,
        y: 800,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 500,
        y: 800,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 300,
        y: 450,
        width: 400,
        height: 100,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Participation Category Template 3
  {
    id: 'participation-template-3',
    name: 'participation-template-3',
    displayName: 'Conference Participation Certificate',
    description: 'Professional certificate for conference and summit participation',
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'conference', 'summit', 'professional', 'networking'],
    preview: '/templates/participation-3.png',
    backgroundImage: '/templates/participation-3.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,
    seoTitle: 'Conference Participation Certificate Template',
    seoDescription: 'Create professional participation certificates for conferences and summits.',
    seoKeywords: ['conference participation certificate', 'summit certificate template'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #DC2626',
      colors: { primary: '#DC2626', secondary: '#EF4444', background: '#FFFFFF', text: '#1F2937', border: '#DC2626' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 300, y: 350, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 300, y: 450, width: 400, height: 100, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 100, y: 800, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 500, y: 800, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-\.\']+$/, datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/, signaturePattern: /^[a-zA-Z\s\-\.\']+$/, detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/ }
  },

  // Participation Category Template 4
  {
    id: 'participation-template-4',
    name: 'participation-template-4',
    displayName: 'Training Participation Certificate',
    description: 'Modern certificate for training program participation',
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'training', 'program', 'modern', 'development'],
    preview: '/templates/participation-4.png',
    backgroundImage: '/templates/participation-4.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,
    seoTitle: 'Training Participation Certificate Template',
    seoDescription: 'Create modern participation certificates for training programs.',
    seoKeywords: ['training participation certificate', 'program certificate template'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #7C3AED',
      colors: { primary: '#7C3AED', secondary: '#A855F7', background: '#FFFFFF', text: '#1F2937', border: '#7C3AED' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 300, y: 350, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 300, y: 450, width: 400, height: 100, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 100, y: 800, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 500, y: 800, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-\.\']+$/, datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/, signaturePattern: /^[a-zA-Z\s\-\.\']+$/, detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/ }
  },

  // Participation Category Template 5
  {
    id: 'participation-template-5',
    name: 'participation-template-5',
    displayName: 'Webinar Participation Certificate',
    description: 'Digital-friendly certificate for webinar participation',
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'webinar', 'digital', 'online', 'learning'],
    preview: '/templates/participation-1.png',
    backgroundImage: '/templates/participation-1.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,
    seoTitle: 'Webinar Participation Certificate Template',
    seoDescription: 'Create digital participation certificates for webinars and online events.',
    seoKeywords: ['webinar participation certificate', 'online event certificate template'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #0891B2',
      colors: { primary: '#0891B2', secondary: '#06B6D4', background: '#FFFFFF', text: '#1F2937', border: '#0891B2' },
      fonts: {
        name: { family: 'Inter', size: 28, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Inter', size: 18, weight: 500, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 300, y: 350, width: 400, height: 50, align: 'center', fontSize: 28, fontFamily: 'Inter', color: '#1F2937', fontWeight: 600 },
      details: { x: 300, y: 450, width: 400, height: 100, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 100, y: 800, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 500, y: 800, width: 150, height: 30, align: 'right', fontSize: 18, fontFamily: 'Inter', color: '#1F2937', fontWeight: 500 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-\.\']+$/, datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/, signaturePattern: /^[a-zA-Z\s\-\.\']+$/, detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/ }
  },
  {
    id: 'participation-template-6',
    name: 'participation-template-6',
    displayName: 'Event Participation Certificate',
    description: 'Professional certificate for event and workshop participation',

    // 分类信息
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'event', 'workshop', 'professional', 'modern'],

    // 视觉属性
    preview: '/templates/participation-1.png',
    backgroundImage: '/templates/participation-1.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Event Participation Certificate Template | Workshop & Conference Certificates',
    seoDescription: 'Create professional participation certificates for events, workshops, and conferences. Perfect for acknowledging attendance and engagement.',
    seoKeywords: ['event participation certificate', 'workshop certificate template', 'conference participation', 'professional event certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      name: {
        x: 300,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 100,
        y: 800,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 500,
        y: 800,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 300,
        y: 450,
        width: 400,
        height: 100,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Excellence Category Template 1
  {
    id: 'excellence-template-1',
    name: 'excellence-template-1',
    displayName: 'Outstanding Excellence Certificate',
    description: 'Premium certificate for outstanding performance and excellence',

    // 分类信息
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'outstanding', 'premium', 'performance', 'merit'],

    // 视觉属性
    preview: '/templates/2 - Copy.png',
    backgroundImage: '/templates/2 - Copy.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Outstanding Excellence Certificate Template | Premium Performance Awards',
    seoDescription: 'Create premium excellence certificates for outstanding performance. Perfect for recognizing exceptional achievements and merit awards.',
    seoKeywords: ['outstanding excellence certificate', 'premium performance award', 'merit certificate template', 'exceptional achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      name: {
        x: 400,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 150,
        y: 150,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 650,
        y: 150,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 200,
        y: 250,
        width: 600,
        height: 80,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Excellence Category Template 2
  {
    id: 'excellence-template-2',
    name: 'excellence-template-2',
    displayName: 'Distinguished Excellence Certificate',
    description: 'Distinguished certificate for exceptional performance and excellence',

    // 分类信息
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'distinguished', 'exceptional', 'premium', 'sophisticated'],

    // 视觉属性
    preview: '/templates/4 - Copy.jpg',
    backgroundImage: '/templates/4 - Copy.jpg',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Distinguished Excellence Certificate Template | Exceptional Performance Awards',
    seoDescription: 'Create distinguished excellence certificates for exceptional performance. Perfect for recognizing top performers and sophisticated achievements.',
    seoKeywords: ['distinguished excellence certificate', 'exceptional performance award', 'sophisticated certificate template', 'premium excellence recognition'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      name: {
        x: 400,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 150,
        y: 150,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 650,
        y: 150,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 200,
        y: 250,
        width: 600,
        height: 80,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Excellence Category Template 3
  {
    id: 'excellence-template-3',
    name: 'excellence-template-3',
    displayName: 'Superior Excellence Certificate',
    description: 'Superior design for exceptional excellence recognition',
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'superior', 'exceptional', 'premium', 'recognition'],
    preview: '/templates/1.png',
    backgroundImage: '/templates/1.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,
    seoTitle: 'Superior Excellence Certificate Template',
    seoDescription: 'Create superior excellence certificates for exceptional achievements.',
    seoKeywords: ['superior excellence certificate', 'exceptional achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #F59E0B',
      colors: { primary: '#F59E0B', secondary: '#FCD34D', background: '#FFFFFF', text: '#1F2937', border: '#F59E0B' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 400, y: 300, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 400, y: 350, width: 500, height: 80, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 150, y: 650, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 445, y: 650, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-\.\']+$/, datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/, signaturePattern: /^[a-zA-Z\s\-\.\']+$/, detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/ }
  },

  // Excellence Category Template 4
  {
    id: 'excellence-template-4',
    name: 'excellence-template-4',
    displayName: 'Distinguished Excellence Certificate',
    description: 'Distinguished design for highest level excellence',
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'distinguished', 'highest', 'premium', 'elite'],
    preview: '/templates/3.jpg',
    backgroundImage: '/templates/3.jpg',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,
    seoTitle: 'Distinguished Excellence Certificate Template',
    seoDescription: 'Create distinguished excellence certificates for highest achievements.',
    seoKeywords: ['distinguished excellence certificate', 'elite achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #7C2D12',
      colors: { primary: '#7C2D12', secondary: '#DC2626', background: '#FFFFFF', text: '#1F2937', border: '#7C2D12' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 400, y: 300, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 400, y: 350, width: 500, height: 80, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 150, y: 650, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 445, y: 650, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-\.\']+$/, datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/, signaturePattern: /^[a-zA-Z\s\-\.\']+$/, detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/ }
  },

  // Excellence Category Template 5
  {
    id: 'excellence-template-5',
    name: 'excellence-template-5',
    displayName: 'Platinum Excellence Certificate',
    description: 'Platinum-level design for ultimate excellence recognition',
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'platinum', 'ultimate', 'luxury', 'prestige'],
    preview: '/templates/2 - Copy.png',
    backgroundImage: '/templates/2 - Copy.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,
    seoTitle: 'Platinum Excellence Certificate Template',
    seoDescription: 'Create platinum excellence certificates for ultimate achievements.',
    seoKeywords: ['platinum excellence certificate', 'luxury achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #6B7280',
      colors: { primary: '#6B7280', secondary: '#9CA3AF', background: '#FFFFFF', text: '#1F2937', border: '#6B7280' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 400, y: 300, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 400, y: 350, width: 500, height: 80, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 150, y: 650, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 445, y: 650, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-\.\']+$/, datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/, signaturePattern: /^[a-zA-Z\s\-\.\']+$/, detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/ }
  },
];

/**
 * 根据ID获取模板
 */
export function getTemplateById(id: string): CertificateTemplate | undefined {
  return CERTIFICATE_TEMPLATES.find(template => template.id === id);
}

/**
 * 获取默认模板
 */
export function getDefaultTemplate(): CertificateTemplate {
  return CERTIFICATE_TEMPLATES[0];
}

/**
 * 获取所有模板ID
 */
export function getAllTemplateIds(): string[] {
  return CERTIFICATE_TEMPLATES.map(template => template.id);
}

/**
 * 验证模板ID是否有效
 */
export function isValidTemplateId(id: string): boolean {
  return CERTIFICATE_TEMPLATES.some(template => template.id === id);
}

/**
 * 模板分类映射配置
 * 用于将现有模板分配到不同分类
 */
export const TEMPLATE_CATEGORY_MAPPING = {
  // Achievement (成就证书)
  'achievement-template-1': CertificateCategory.ACHIEVEMENT,
  'achievement-template-2': CertificateCategory.ACHIEVEMENT,

  // Completion (完成证书)
  'completion-template-1': CertificateCategory.COMPLETION,
  'completion-template-2': CertificateCategory.COMPLETION,

  // Participation (参与证书)
  'participation-template-1': CertificateCategory.PARTICIPATION,
  'participation-template-2': CertificateCategory.PARTICIPATION,

  // Excellence (优秀证书)
  'excellence-template-1': CertificateCategory.EXCELLENCE,
  'excellence-template-2': CertificateCategory.EXCELLENCE
} as const;

/**
 * 根据分类获取模板
 */
export function getTemplatesByCategory(category: CertificateCategory): CertificateTemplate[] {
  return CERTIFICATE_TEMPLATES.filter(template => template.category === category);
}

/**
 * 根据模板名称获取模板
 */
export function getTemplateByName(name: string): CertificateTemplate | undefined {
  return CERTIFICATE_TEMPLATES.find(template => template.name === name);
}
